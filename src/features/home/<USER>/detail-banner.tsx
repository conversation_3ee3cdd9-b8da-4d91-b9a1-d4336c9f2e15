import { Link } from '@tanstack/react-router'
import { useAtom, useAtomValue } from 'jotai'
import { useMemo } from 'react'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'

import { fundIdAtom, getFundNamesQueryAtom } from './../atoms'

export function DetailBanner() {
  const id = useAtomValue(fundIdAtom)
  const { data, isLoading } = useAtomValue(getFundNamesQueryAtom)
  const [, setFundIdAtom] = useAtom(fundIdAtom)

  const defaultValue = useMemo(() => {
    return id || (data?.fundNames?.[0]?.value || '')
  }, [id, data])

  return (
    <div className="flex justify-between">
      <Button>
        <Link to="/">Back</Link>
      </Button>
      <div>
        {isLoading ? (
          <Skeleton className="h-10 w-24" />
        ) : (
          <Select defaultValue={defaultValue} onValueChange={setFundIdAtom}>
            <SelectTrigger>
              <SelectValue placeholder="基金名称" />
            </SelectTrigger>
            <SelectContent>
              {data?.fundNames.map(({ label, value }) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  )
}

import { useAtomValue } from 'jotai'
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react'
import { useMemo, useState } from 'react'

import { HomeSearch } from '@/components/home-search'
import { Button } from '@/components/ui/button'
import { cn } from '@/helpers/utils'

import { getFundOptionsQueryAtom } from '../atoms'

export function HomeBanner() {
  const { data } = useAtomValue(getFundOptionsQueryAtom)

  const [more, setMore] = useState(false)
  const fundOptions = useMemo(() => {
    if (!data) {
      return {
        fundNames: [],
        companies: [],
        fundTypes: [],
      }
    }
    const companies = data.companies.map(name => ({
      label: name,
      value: name,
    }))
    return {
      fundNames: data.fundNames,
      companies,
      fundTypes: data.fundTypes,
    }
  }, [data])

  const handleChange = () => {
    console.log('change')
  }

  return (
    <div className="container">
      <div className="mt-[118px]">
        <div className="bg-[url('/src/assets/home-title.png')] w-[363px] h-[78px] bg-cover bg-center bg-no-repeat" />
        <div className="mt-[48px] w-[1215px]">
          <div className={cn('h-[118px] overflow-hidden', more ? 'h-auto' : '')}>
            <div className="text-[22px] font-normal leading-[38px] tracking-[0px] text-white text-left align-top">
              <div>气候变化已经成为威胁全球生态平衡和社会经济稳定的重大风险之一。</div>
              <div>
                作为金融体系的重要组成部分，资产管理机构在引导资源配置、推动可持续发展方面具备独特优势。凭借广泛的资本运作能力和投资决策影响力，该行业能够为实体经济中的低碳项日提供关键支持，助力绿色转型进程，同时实现风险管理与长期收益的平衡。
                <div>在国际层面，ESG 投资与绿色金融持续升温，监管标准不断提升，各类"洗绿"行为受到更多审视。</div>
                中国在"双碳"战略和政策引导下，责任投资市场不断扩容，绿色资金供给日益增强，投资者对资管公司的"绿色"程度关注度更喜。因此，本数据库评估对比了国内头部资产管理公司发行的几十支可持续主题基金产品针对高碳排放行业 / 公司的投资情况，从而为关注可持续投融资的相关投资者、学者以及其他利益相关方提供数据参考，帮助甄别投资产品的"洗绿"风险。
              </div>
            </div>
            <div className="text-[22px] font-medium leading-[38px] tracking-[0px] text-white text-left align-top">本数据库覆盖自 2023 年起的相关数据，并将持续更新。</div>

          </div>
          <Button className="rounded-[3px] border-[0.6px] border-[#A6A6A6] text-[#A6A6A6] bg-transparent" onClick={() => setMore(!more)}>
            {
              more ? (
                <>
                  收起更多
                  <ChevronUpIcon />
                </>
              ) : (
                <>
                  展开全部
                  <ChevronDownIcon />
                </>
              )
            }
          </Button>
          {/* 下拉搜索框 */}
          <HomeSearch
            options={fundOptions.fundNames}
            onValueChange={(val) => {
              handleChange('fundNames', val)
            }}
            placeholder="请输入基金名称"
            variant="inverted"
            animation={2}
            maxCount={3}
          />
          {/* <div className="mt-6 flex items-center w-[600px] h-[48px] bg-[#fff] rounded-[8px] shadow-[0_2px_8px_0_rgba(0,0,0,0.15)] overflow-hidden"> */}
          {/*   <div className="flex items-center flex-1 px-4"> */}
          {/*     <svg className="w-6 h-6 text-[#A6A6A6] mr-2" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"> */}
          {/*       <circle cx="11" cy="11" r="8" /> */}
          {/*       <line x1="21" y1="21" x2="16.65" y2="16.65" /> */}
          {/*     </svg> */}
          {/*     <input */}
          {/*       className="flex-1 outline-none border-none bg-transparent text-[#333] text-[18px] placeholder-[#A6A6A6]" */}
          {/*       placeholder="请输入您想查询的基金名称" */}
          {/*       type="text" */}
          {/*     /> */}
          {/*     <ChevronDownIcon className="w-5 h-5 text-[#A6A6A6] ml-2" /> */}
          {/*   </div> */}
          {/*   <Button className="h-full px-6 bg-[#6EFF3E] text-[#222] text-[18px] font-medium whitespace-nowrap hover:bg-[#5be62e] transition-colors"> */}
          {/*     查看基金详情 */}
          {/*   </Button> */}
          {/* </div> */}
        </div>
      </div>
    </div>
  )
}

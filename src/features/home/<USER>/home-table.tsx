import { useNavigate } from '@tanstack/react-router'
import type {
  Cell,
  Column,
  ColumnDef,
  Header,
  HeaderGroup,
  Row,
} from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { useAtomValue } from 'jotai'
import { ArrowUpDown } from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { encodeParam } from '@/helpers/lib/encoding'

import type { FundInfo } from '../atoms'
import { getFundListQueryAtom } from '../atoms'

type SortDirection = 'asc' | 'desc' | false

function SortButton({
  column,
  title,
}: {
  column: Column<FundInfo, unknown>
  title: string
}) {
  const sortDirection = column.getIsSorted() as SortDirection

  return (
    <Button
      variant="ghost"
      onClick={() => {
        if (!sortDirection) {
          column.toggleSorting(true)
        }
        else if (sortDirection === 'asc') {
          column.toggleSorting(false)
        }
        else {
          column.clearSorting()
        }
      }}
      className="h-8 w-8 p-0"
    >
      <ArrowUpDown className="h-4 w-4" />
      <span className="sr-only">{title}</span>
    </Button>
  )
}

export function HomeTable() {
  const navigate = useNavigate()
  const { data = [], isLoading } = useAtomValue(getFundListQueryAtom)

  const columns: Array<ColumnDef<FundInfo>> = [
    {
      accessorKey: 'windCode',
      header: 'WIND 代码',
    },
    {
      accessorKey: 'fundName',
      header: '基金名称',
      cell: ({ row }) => {
        const { windCode, fundName } = row.original
        return (
          <span
            className="cursor-pointer text-primary hover:underline"
            onClick={() =>
              navigate({ to: '/detail', search: { id: encodeParam(windCode) } })}
          >
            {fundName}
          </span>
        )
      },
    },
    {
      accessorKey: 'fundCompany',
      header: '基金公司',
    },
    {
      accessorKey: 'fundType',
      header: '基金类型',
    },
    {
      accessorKey: 'issueDate',
      header: ({ column }) => (
        <div className="flex items-center justify-between">
          <span>发行日期</span>
          <SortButton column={column} title="按发行日期排序" />
        </div>
      ),
      sortingFn: 'datetime',
    },
    {
      accessorKey: 'highCarbonRatio',
      header: ({ column }) => (
        <div className="flex items-center justify-between">
          <span>高碳相关投资比例</span>
          <SortButton column={column} title="按高碳相关投资比例排序" />
        </div>
      ),
    },
    {
      accessorKey: 'fossilFuelRatio',
      header: ({ column }) => (
        <div className="flex items-center justify-between">
          <span>化石燃料相关投资比例</span>
          <SortButton column={column} title="按化石燃料相关投资比例排序" />
        </div>
      ),
    },
    {
      accessorKey: 'fundSize',
      header: ({ column }) => (
        <div className="flex items-center justify-between">
          <span>基金规模（万元）</span>
          <SortButton column={column} title="按基金规模排序" />
        </div>
      ),
    },
    {
      accessorKey: 'fundSizeUpdateDate',
      header: ({ column }) => (
        <div className="flex items-center justify-between">
          <span>基金规模更新日期</span>
          <SortButton column={column} title="按基金规模更新日期排序" />
        </div>
      ),
      sortingFn: 'datetime',
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  return (
    <div className="rounded-md border">
      <div className="h-[500px] overflow-auto">
        <Table>
          <TableHeader className="bg-background sticky top-0">
            {table
              .getHeaderGroups()
              .map((headerGroup: HeaderGroup<FundInfo>) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(
                    (header: Header<FundInfo, unknown>) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}
                        </TableHead>
                      )
                    },
                  )}
                </TableRow>
              ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Loading...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length
              ? (
                  table.getRowModel().rows.map((row: Row<FundInfo>) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                    >
                      {row
                        .getVisibleCells()
                        .map((cell: Cell<FundInfo, unknown>) => (
                          <TableCell key={cell.id}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext(),
                            )}
                          </TableCell>
                        ))}
                    </TableRow>
                  ))
                )
              : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

import { useAtomValue } from 'jotai'

import { PageFooter } from '@/components/page-footer'
import { PageHeader } from '@/components/page-header'
import { Button } from '@/components/ui/button'
import type { POST_API_OUTPUTS } from '@/helpers/lib/api-client/types'

import type { QueryParams } from './atoms'
import { downloadHomeList, queryFormAtom } from './atoms'
import { HomeBanner } from './components/home-banner'
import { HomeForm } from './components/home-form'
import { HomeTable } from './components/home-table'
import { useDownload } from './hooks/useDownload'

export function HomePage() {
  const queryParams = useAtomValue(queryFormAtom)
  const fileName = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} Finance of Future Fund List.xlsx`
  const { isPending, download } = useDownload<QueryParams, POST_API_OUTPUTS<'/api/funds/download-home'>>(downloadHomeList, fileName)

  async function handleDownload() {
    await download(queryParams)
  }

  return (
    <div className="flex min-h-screen flex-col">
      <div className="relative bg-[url('/src/assets/home-banner.jpg')] bg-cover bg-center bg-no-repeat h-[800px]">
        <PageHeader />
        <div className="container">
          <HomeBanner />
        </div>
      </div>
      <div className="p-6">
        <div>
          <HomeForm />
        </div>
        <div className="">
          <HomeTable />
        </div>
        <div className="mt-6 flex justify-end">
          <Button onClick={handleDownload} disabled={isPending}>
            {isPending ? '下载中...' : '下载数据'}
          </Button>
        </div>
      </div>
      <PageFooter />
    </div>
  )
}

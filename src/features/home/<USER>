import { atom } from 'jotai'
import { atomWithQuery } from 'jotai-tanstack-query'
import { toast } from 'sonner'

import { api } from '@/helpers/lib/api-client'
import type { POST_API_DATA, POST_API_INPUTS, POST_API_OUTPUTS } from '@/helpers/lib/api-client/types'

export type FundInfo = POST_API_DATA<'/api/funds/get-list'>['data'][number]

export type QueryParams = {
  fundNames?: string[]
  companies?: string[]
  fundType?: string
  issueDateStart?: string
  issueDateEnd?: string
}

export const fundIdAtom = atom<string>()
export const selectedYearAtom = atom<string>('')
export const yearListAtom = atom<string[]>([])

export const markAtom = atom<POST_API_INPUTS<'/api/funds/get-stocks-list'>['mark']>('ff')

export const stockPaginationAtom = atom({
  page: 1,
  size: 10,
})

export const queryFormAtom = atom<QueryParams>({
  fundNames: undefined,
  companies: undefined,
  fundType: undefined,
  issueDateStart: undefined,
  issueDateEnd: undefined,
})

export const queryParamsAtom = atom<QueryParams | null>(null)
export const syncQueryParamsAtom = atom(null, (get, set) => {
  const formValues = get(queryFormAtom)
  set(queryParamsAtom, formValues)
})

export const getFundListQueryAtom = atomWithQuery((get) => {
  const params = get(queryParamsAtom)
  return {
    queryKey: ['fundList', params],
    queryFn: async () => {
      const res = await api.post('/api/funds/get-list', {
        fundNames: params?.fundNames,
        companies: params?.companies,
        fundType: params?.fundType,
        issueDateStart: params?.issueDateStart,
        issueDateEnd: params?.issueDateEnd,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export type FundOptions = POST_API_DATA<'/api/funds/get-options'>['data']

export const getFundOptionsQueryAtom = atomWithQuery(() => ({
  queryKey: ['fundOptions'],
  queryFn: async () => {
    const res = await api.post('/api/funds/get-options')
    if (res.status === 'success') {
      return res.data
    }
    else {
      toast.error(res.data)
    }
  },
}))

export const getFundNamesQueryAtom = atomWithQuery(() => ({
  queryKey: ['fundNames'],
  queryFn: async () => {
    const res = await api.post('/api/funds/get-names')
    if (res.status === 'success') {
      return res.data
    }
    else {
      toast.error(res.data)
    }
  },
}))

export const getCarbonInvestmentQueryAtom = atomWithQuery((get) => {
  const params = get(fundIdAtom)
  return {
    queryKey: ['carbonInvestment', params],
    queryFn: async () => {
      const res = await api.post('/api/funds/get-carbon-investment', {
        windId: params!,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export const getCompanyTypeStatisticsQueryAtom = atomWithQuery((get) => {
  const params = get(fundIdAtom)
  const year = get(selectedYearAtom)

  return {
    queryKey: ['carbonInvestment', params, year],
    queryFn: async () => {
      const res = await api.post('/api/funds/company-type-statistics', {
        windId: params!,
        year,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export const getYearsQueryAtom = atomWithQuery(get => ({
  queryKey: ['years', get(fundIdAtom)],
  queryFn: async () => {
    const res = await api.post('/api/funds/get-years', {
      windId: get(fundIdAtom)!,
    })
    if (res.status === 'success') {
      return res.data
    }
    else {
      toast.error(res.data)
    }
  },
}))

export type TopHoldingsInfo = POST_API_DATA<'/api/funds/get-top-holdings'>['data'][number]

export const getTopHoldingsQueryAtom = atomWithQuery((get) => {
  const windId = get(fundIdAtom)
  const year = get(selectedYearAtom)
  return {
    queryKey: ['top', windId, year],
    queryFn: async () => {
      if (!year) {
        return []
      }
      const res = await api.post('/api/funds/get-top-holdings', {
        windId: windId!,
        year,
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export const getStocksByMarkQueryAtom = atomWithQuery((get) => {
  const windId = get(fundIdAtom)
  const year = get(selectedYearAtom)
  const mark = get(markAtom)
  const pagination = get(stockPaginationAtom)

  return {
    queryKey: ['stocks', windId, year, mark, pagination],
    queryFn: async () => {
      const res = await api.post('/api/funds/get-stocks-list', {
        windId: windId!,
        year,
        mark,
        paging: {
          page: pagination.page,
          size: pagination.size,
        },
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  }
})

export async function downloadHomeList(data: POST_API_INPUTS<'/api/funds/download-home'>) {
  return api.post('/api/funds/download-home', data)
}

export type DownloadTopHoldingsParams = POST_API_INPUTS<'/api/funds/download-top-holdings'>
export type DownLoadTopHoldingsResult = POST_API_OUTPUTS<'/api/funds/download-top-holdings'>

export async function downloadTopHoldings(data: DownloadTopHoldingsParams) {
  return api.post('/api/funds/download-top-holdings', data)
}

export type DownloadStocksListParams = POST_API_INPUTS<'/api/funds/download-stock-list'>
export type DownLoadStocksListResult = POST_API_OUTPUTS<'/api/funds/download-stock-list'>

export async function downloadStocksList(data: DownloadStocksListParams) {
  return api.post('/api/funds/download-stock-list', data)
}

import { useMemo } from 'react'

import type { POST_API_DATA } from '@/helpers/lib/api-client/types'

const Option = {
  title: {
    left: 'center',
  },
  tooltip: {
    trigger: 'item',
  },
}

export type UseOptionProps = {
  data?: POST_API_DATA<'/api/funds/company-type-statistics'>['data']
}

export function usePieOption({ data }: UseOptionProps) {
  const ffOption = useMemo(() => {
    return {
      ...Option,
      title: {
        ...Option.title,
        text: '化石燃料相关行业投资公司数量占比',
      },
      series: [
        {
          name: '化石燃料相关行业投资公司数量占比',
          type: 'pie',
          data: [
            { value: data?.ff?.notIn || 0, name: '非化石燃料相关', mark: 'nt_ff' },
            { value: data?.ff?.in || 0, name: '化石燃料相关', mark: 'ff' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }
  }, [data?.ff])

  const gtOption = useMemo(() => {
    return {
      ...Option,
      title: {
        ...Option.title,
        text: '高碳类型行业公司数量占比',
      },
      series: [
        {
          name: '高碳类型行业公司数量占比',
          type: 'pie',
          data: [
            { value: data?.gt?.notIn || 0, name: '非高碳', mark: 'nt_gt' },
            { value: data?.gt?.in || 0, name: '高碳', mark: 'gt' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }
  }, [data?.gt])

  return {
    ffOption,
    gtOption,
  }
}

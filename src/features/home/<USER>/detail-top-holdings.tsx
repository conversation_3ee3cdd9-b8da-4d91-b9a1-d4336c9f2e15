import type { ColumnDef } from '@tanstack/react-table'
import { flexRender, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table'
import { useAtomValue } from 'jotai'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import type { TopHoldingsInfo } from '../atoms'
import { getTopHoldingsQueryAtom } from '../atoms'

export function DetailTopHoldings() {
  const { data = [] } = useAtomValue(getTopHoldingsQueryAtom)

  const columns: Array<ColumnDef<TopHoldingsInfo>> = [
    {
      accessorKey: 'stockName',
      header: '股票名称',
    },
    {
      accessorKey: 'stockCode',
      header: '股票代码',
    },
    {
      accessorKey: 'proportiontototalstockinvestments',
      header: '投资占比%',
      cell: ({ row }) => `${(row.getValue('proportiontototalstockinvestments') as number).toFixed(2)}`,
    },
    {
      accessorKey: 'FFMark',
      header: '是否化石',
    },
    {
      accessorKey: 'GTMark',
      header: '是否高碳',
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  return (
    <div className="h-[350px] pt-4 bg-white rounded shadow">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map(header => (
                <TableHead key={header.id}>
                  {typeof header.column.columnDef.header === 'string'
                    ? header.column.columnDef.header
                    : null}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map(row => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => {
                const cellDef = cell.column.columnDef.cell
                return (
                  <TableCell key={cell.id}>
                    {flexRender(cellDef, cell.getContext())}
                  </TableCell>
                )
              })}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

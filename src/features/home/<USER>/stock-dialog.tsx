import type { ColumnDef } from '@tanstack/react-table'
import { flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'
import { useAtom, useAtomValue } from 'jotai'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import type { DownloadStocksListParams, DownLoadStocksListResult } from '../atoms'
import { downloadStocksList, fundIdAtom, getStocksByMarkQueryAtom, markAtom, selectedYear<PERSON>tom, stockPaginationAtom } from '../atoms'
import { useDownload } from '../hooks/useDownload'

type StockData = {
  stockName: string
  stockCode: string
  rptDate: string
  proportiontototalstockinvestments: number
  windIndustry: string | null
  gpType: string | null
  FFMark: string | null
  GTMark: string | null
}

export type StockDialogProps = {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
  title?: string
}

export function StockDialog({ isOpen, setIsOpen, title }: StockDialogProps) {
  const { data } = useAtomValue(getStocksByMarkQueryAtom)
  const [pagination, setPagination] = useAtom(stockPaginationAtom)
  // ${已选择的基金名称}${已选择的报告时间}${已选择的公司分类}公司列表
  const fileName = `公司列表.xlsx`
  const { isPending, download } = useDownload<DownloadStocksListParams, DownLoadStocksListResult>(downloadStocksList, fileName)

  const [windId] = useAtom(fundIdAtom)
  const [year] = useAtom(selectedYearAtom)
  const [mark] = useAtom(markAtom)

  const columns: Array<ColumnDef<StockData>> = [
    {
      accessorKey: 'stockName',
      header: '股票名称',
    },
    {
      accessorKey: 'stockCode',
      header: '股票代码',
    },
    {
      accessorKey: 'rptDate',
      header: '报告时间',
    },
    {
      accessorKey: 'proportiontototalstockinvestments',
      header: '投资占比',
      cell: ({ row }) => `${(row.getValue('proportiontototalstockinvestments') as number).toFixed(2)}%`,
    },
    {
      accessorKey: 'windIndustry',
      header: 'WIND 行业名称',
    },
    {
      accessorKey: 'gpType',
      header: '本数据库行业分类',
    },
    {
      accessorKey: 'FFMark',
      header: '是否所属化石燃料相关行业',
    },
    {
      accessorKey: 'GTMark',
      header: '是否所属高碳类型行业',
    },
  ]

  const table = useReactTable({
    data: (data?.list || []) as StockData[],
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil((data?.pagination?.total || 0) / pagination.size),
    state: {
      pagination: {
        pageIndex: pagination.page - 1,
        pageSize: pagination.size,
      },
    },
  })

  const handleDownload = () => {
    download({
      windId: windId!,
      year: year!,
      mark: mark!,
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[900px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <Button onClick={handleDownload} className="absolute right-4 top-4">
            {isPending ? '下载中...' : '下载数据'}
          </Button>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="rounded-md border overflow-auto h-[400px]">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map(row => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                    >
                      {row.getVisibleCells().map(cell => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      暂无数据
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={!data?.pagination.total || pagination.page >= Math.ceil(data.pagination.total / pagination.size)}
            >
              下一页
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

import ReactECharts from 'echarts-for-react'
import { useAtom, useAtomValue } from 'jotai'
import { useMemo, useState } from 'react'

import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import type { DownloadTopHoldingsParams, DownLoadTopHoldingsResult } from '../atoms'
import { downloadTopHoldings, fundIdAtom, getCompanyTypeStatisticsQueryAtom, getYearsQueryAtom, markAtom, selectedYearAtom, stockPaginationAtom } from '../atoms'
import { useDownload } from '../hooks/useDownload'
import { usePieOption } from '../hooks/usePieOption'
import { StockDialog } from './stock-dialog'

export function DetailPie() {
  const { data } = useAtomValue(getCompanyTypeStatisticsQueryAtom)
  const { data: yearList } = useAtomValue(getYearsQuery<PERSON>tom)
  const [year, setYear] = useAtom(selectedYear<PERSON>tom)
  const [windId] = useAtom(fundIdAtom)
  const [, setMark] = useAtom(markAtom)
  const [, setPagination] = useAtom(stockPaginationAtom)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedData, setSelectedData] = useState<{
    title: string
    name: string
    value: number
    percent: number
  } | null>(null)

  useMemo(() => {
    if (yearList && yearList.length > 0 && !year) {
      setYear(yearList[0])
    }
  }, [yearList, year, setYear])

  const { ffOption, gtOption } = usePieOption({ data })

  // YYYY-MM-DD ${已选择的基金名称} ${已选择的报告时间} 持仓信息
  const fileName = `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()} ${windId} ${year} 持仓信息.xlsx`
  const { isPending, download } = useDownload<DownloadTopHoldingsParams, DownLoadTopHoldingsResult>(downloadTopHoldings, fileName)

  const handleYearChange = (value: string) => {
    setYear(value)
  }

  const handleFFChartClick = (params: any) => {
    setMark(params.data.mark)
    setPagination(prev => ({ ...prev, page: 1 }))
    setSelectedData({
      title: '化石燃料相关行业投资公司数量占比',
      name: params.name,
      value: params.value,
      percent: params.percent,
    })
    setIsDialogOpen(true)
  }

  const handleGTChartClick = (params: any) => {
    setMark(params.data.mark)
    setPagination(prev => ({ ...prev, page: 1 }))
    setSelectedData({
      title: '高碳类型行业公司数量占比',
      name: params.name,
      value: params.value,
      percent: params.percent,
    })
    setIsDialogOpen(true)
  }

  async function handleDownload() {
    await download({
      windId: windId!,
      year,
    })
  }

  return (
    <div className="flex flex-col gap-4 mt-4">
      <div className="flex justify-between">
        <div>
          <Select onValueChange={handleYearChange} value={year}>
            <SelectTrigger>
              <SelectValue placeholder="选择年份" />
            </SelectTrigger>
            <SelectContent>
              {yearList?.map(year => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Button onClick={handleDownload} disabled={isPending}>
          {isPending ? '下载中...' : '下载数据'}
        </Button>

      </div>
      <div className="h-[350px] pt-4 bg-white rounded shadow">
        {year && (
          <div className="flex flex-row gap-4">
            <ReactECharts
              option={ffOption}
              style={{ height: 320, width: '50%' }}
              onEvents={{
                click: handleFFChartClick,
              }}
            />
            <ReactECharts
              option={gtOption}
              style={{ height: 320, width: '50%' }}
              onEvents={{
                click: handleGTChartClick,
              }}
            />
          </div>
        )}
      </div>
      {isDialogOpen && <StockDialog isOpen={isDialogOpen} setIsOpen={setIsDialogOpen} title={selectedData?.title} />}
    </div>
  )
}

import { ChevronDown, Search } from 'lucide-react'
import { useState } from 'react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function FundSearch() {
  const [search, setSearch] = useState('')
  const [selected, setSelected] = useState('全部基金')

  return (
    <div className="flex items-center w-full max-w-xl rounded-md overflow-hidden shadow-md border border-gray-300">
      {/* 搜索图标 */}
      <div className="pl-3 pr-2 text-gray-500">
        <Search className="w-5 h-5" />
      </div>

      {/* 输入框 */}
      <Input
        className="border-none focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-gray-400 flex-1"
        placeholder="请输入您想查询的基金名称"
        value={search}
        onChange={e => setSearch(e.target.value)}
      />

      {/* 下拉按钮（伪装为选择器） */}
      <Button
        variant="ghost"
        className="rounded-none border-l border-gray-200 px-2 text-gray-700 hover:bg-gray-100"
        onClick={() => alert('展示下拉菜单')}
      >
        <ChevronDown className="w-4 h-4" />
      </Button>

      {/* 绿色按钮 */}
      <Button
        className="bg-lime-400 hover:bg-lime-500 text-black rounded-none px-4"
        onClick={() => alert(`查询：${search}`)}
      >
        查看基金详情
      </Button>
    </div>
  )
}

import { useState } from 'react'

import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select'

import { Input } from './ui/input'

export function HomeSearch() {
  // 斜体单独用一个区域设置，zindex高于左右
  const [value, setValue] = useState('')

  return (
    <Select onValueChange={setValue}>
      <SelectTrigger className="w-[180px]">
        <Input value={value} className="w-full" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="light">Light</SelectItem>
        <SelectItem value="dark">Dark</SelectItem>
        <SelectItem value="system">System</SelectItem>
      </SelectContent>
    </Select>
  )
}
